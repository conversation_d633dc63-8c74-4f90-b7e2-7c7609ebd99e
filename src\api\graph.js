import request from './axios';

/**
 * 获取知识图谱列表
 * @returns {Promise<Array>} 图谱列表
 */
export const getGraphList = () => {
  console.log('正在获取知识图谱列表...');
  return request({
    url: '/core/zstp/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 100 // 增加默认获取数量到100个
    }
  }).then(response => {
    console.log('获取知识图谱列表成功:', response);
    // 处理嵌套的响应结构
    if (response && typeof response === 'object') {
      if (response.rows && Array.isArray(response.rows)) {
        console.log('返回标准响应格式');
        return response;
      } else if (Array.isArray(response)) {
        console.log('返回数组格式');
        return { rows: response };
      } else if (response.data && Array.isArray(response.data)) {
        console.log('从嵌套响应中提取知识图谱列表数据');
        return { rows: response.data };
      }
    }
    console.warn('响应格式不符合预期，返回空列表');
    return { rows: [] };
  }).catch(error => {
    console.error('获取知识图谱列表失败:', error);
    throw error;
  });
};

/**
 * 获取知识图谱详细信息
 * @param {string|number} id 知识图谱ID
 * @returns {Promise<Object>} 知识图谱详细信息
 * @description 根据API文档 GET /core/zstp/{id} 获取指定ID的知识图谱详细信息
 */
export const getGraph = (id) => {
  console.log(`正在获取知识图谱详细信息: ${id}`);

  if (!id) {
    console.error('获取知识图谱详细信息失败: ID不能为空');
    return Promise.reject(new Error('知识图谱ID不能为空'));
  }

  return request({
    url: `/core/zstp/${id}`,
    method: 'get'
  }).then(response => {
    console.log(`获取知识图谱${id}详细信息成功:`, response);

    // 根据API文档，响应包含 error, success, warn, empty 字段
    if (response && typeof response === 'object') {
      if (response.error === true) {
        console.error('API返回错误状态:', response);
        throw new Error('获取知识图谱详细信息失败');
      }

      if (response.success === true) {
        console.log('API返回成功状态');
        return response;
      }
    }

    // 如果响应格式不符合预期，仍然返回原始响应
    console.warn('响应格式不符合API文档预期:', response);
    return response;
  }).catch(error => {
    console.error(`获取知识图谱${id}详细信息失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取知识图谱详细信息（别名函数）
 * @param {string|number} id 知识图谱ID
 * @returns {Promise<Object>} 知识图谱详细信息
 */
export const getZstpDetail = (id) => {
  return getGraph(id);
};

/**
 * 创建新的知识图谱
 * @param {Object} graphData 图谱数据
 * @returns {Promise<Object>} 创建结果
 */
export const createGraph = (graphData) => {
  console.log('创建新知识图谱:', graphData);
  return request({
    url: '/core/zstp',
    method: 'post',
    data: graphData
  }).then(response => {
    console.log('创建知识图谱成功:', response);
    return response;
  }).catch(error => {
    console.error('创建知识图谱失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 更新知识图谱
 * @param {Object} graphData 图谱数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateGraph = (graphData) => {
  console.log('更新知识图谱信息:', graphData);
  return request({
    url: `/core/zstp`,
    method: 'put',
    data: graphData
  }).then(response => {
    console.log('更新知识图谱成功:', response);
    return response;
  }).catch(error => {
    console.error('更新知识图谱失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 查询课程的知识图谱和章节列表
 * @param {string|number} courseId 课程ID
 * @param {string} graphType 图谱类型："0"=知识图谱，"1"=章节，不传则查询所有
 * @returns {Promise<Object>} 知识图谱/章节列表
 */
export const getCourseZstpList = (courseId, graphType = null) => {
  console.log(`正在查询课程${courseId}的知识图谱/章节列表，类型:${graphType}`);

  const params = {
    pageNum: 1,
    pageSize: 100
  };

  // 构建查询参数
  if (courseId) {
    params.courseId = courseId;
  }
  if (graphType !== null) {
    params.graphType = graphType;
  }

  return request({
    url: '/core/zstp/list',
    method: 'get',
    params
  }).then(response => {
    console.log(`获取课程${courseId}知识图谱/章节列表成功:`, response);
    // 处理嵌套的响应结构
    if (response && typeof response === 'object') {
      if (response.rows && Array.isArray(response.rows)) {
        console.log('返回标准响应格式');
        return response;
      } else if (Array.isArray(response)) {
        console.log('返回数组格式');
        return { rows: response };
      } else if (response.data && Array.isArray(response.data)) {
        console.log('从嵌套响应中提取数据');
        return { rows: response.data };
      }
    }
    console.warn('响应格式不符合预期，返回空列表');
    return { rows: [] };
  }).catch(error => {
    console.error(`获取课程${courseId}知识图谱/章节列表失败:`, error);
    throw error;
  });
};

/**
 * 创建知识图谱或章节
 * @param {Object} zstpData ZstpGraph对象
 * @param {string|number} zstpData.courseId 课程ID
 * @param {string} zstpData.graphType 图谱类型："0"=知识图谱，"1"=章节
 * @param {string} zstpData.name 名称
 * @param {string} zstpData.content 内容文本（可选）
 * @param {string} zstpData.remark 备注（可选）
 * @returns {Promise<Object>} 创建结果
 */
export const createZstp = (zstpData) => {
  console.log('创建知识图谱/章节:', zstpData);

  // 验证必填字段
  if (!zstpData.courseId) {
    throw new Error('课程ID不能为空');
  }
  if (!zstpData.graphType) {
    throw new Error('图谱类型不能为空');
  }
  if (!zstpData.name) {
    throw new Error('名称不能为空');
  }

  return request({
    url: '/core/zstp',
    method: 'post',
    data: zstpData
  }).then(response => {
    console.log('创建知识图谱/章节成功:', response);
    return response;
  }).catch(error => {
    console.error('创建知识图谱/章节失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 更新知识图谱或章节
 * @param {Object} zstpData ZstpGraph对象（必须包含id字段）
 * @returns {Promise<Object>} 更新结果
 */
export const updateZstp = (zstpData) => {
  console.log('更新知识图谱/章节:', zstpData);

  if (!zstpData.id) {
    throw new Error('更新时ID不能为空');
  }

  return request({
    url: '/core/zstp',
    method: 'put',
    data: zstpData
  }).then(response => {
    console.log('更新知识图谱/章节成功:', response);
    return response;
  }).catch(error => {
    console.error('更新知识图谱/章节失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除知识图谱或章节
 * @param {string|number} zstpId 知识图谱/章节ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteZstp = (zstpId) => {
  console.log('删除知识图谱/章节:', zstpId);

  if (!zstpId) {
    throw new Error('删除时ID不能为空');
  }

  return request({
    url: `/core/zstp/${zstpId}`,
    method: 'delete'
  }).then(response => {
    console.log('删除知识图谱/章节成功:', response);
    return response;
  }).catch(error => {
    console.error('删除知识图谱/章节失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除知识图谱
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteGraph = (graphId) => {
  console.log(`删除知识图谱: ${graphId}`);
  return request({
    url: `/core/zstp/${graphId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除知识图谱 ${graphId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除知识图谱 ${graphId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取知识图谱大纲
 * @param {string} courseId 知识图谱ID
 * @returns {Promise<Object>} 知识图谱大纲数据
 */
export const getCourseOutline = (courseId) => {
  console.log(`正在获取知识图谱大纲: ${courseId}`);
  return request({
    url: `/api/courses/${courseId}/outline`,
    method: 'get'
  }).then(response => {
    console.log(`获取知识图谱${courseId}大纲成功:`, response);
    // 处理嵌套的响应结构
    if (response && typeof response === 'object') {
      if (response.success && response.data) {
        console.log('从嵌套响应中提取大纲数据');
        return response.data;
      } else if (response.nodes || response.title) {
        return response;
      }
    }
    console.warn('响应格式不符合预期');
    return null;
  }).catch(error => {
    console.error(`获取知识图谱 ${courseId} 大纲失败:`, error);
    throw error;
  });
};

/**
 * 获取知识图谱节点列表
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 节点列表
 */
export const getNodeList = (graphId) => {
  console.log(`正在获取节点数据: ${graphId}`);
  return request({
    url: `core/node/list`,
    method: 'get',
    params: {
      graphId: graphId
    }
  }).then(response => {
    console.log(`获取节点数据:`, response);
    return response.rows;
  }).catch(error => {
    console.error(`获取节点列表失败: ${graphId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取节点样式
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 节点样式
 */
export const getNodeStyle = (nodeId) => {
  return request({
    url: `core/style/${nodeId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取节点样式:`, response);
    return response.data;
  }).catch(error => {
    console.error(`获取节点样式失败: ${nodeId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取节点连线
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 连线列表
 */
export const getNodeLines = (nodeId) => {
  return request({
    url: `core/line/list`,
    method: 'get',
    params: {
      nodeId: nodeId
    }
  }).then(response => {
    console.log(`获取节点线条:`, response);
    return response.rows;
  }).catch(error => {
    console.error(`获取节点线条失败: ${nodeId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 创建节点
 * @param {Object} node 节点数据
 * @returns {Promise<Object>} 创建结果
 */
export const createNode = (node) => {
  return request({
    url: `core/node`,
    method: 'post',
    data: node
  }).then(response => {
    console.log(`创建节点:`, response);
    return response.data;
  }).catch(error => {
    console.error('创建节点失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 更新节点
 * @param {Object} node 节点数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateNode = (node) => {
  return request({
    url: `core/node`,
    method: 'put',
    data: node
  }).then(response => {
    console.log(`更新节点:`, response);
    return response.data;
  }).catch(error => {
    console.error('更新节点失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除节点
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteNode = (nodeId) => {
  return request({
    url: `/core/node/${nodeId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除节点:`, response);
    return response.data;
  }).catch(error => {
    console.error(`删除节点 ${nodeId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存节点数据
 * @param {Object} nodeData 节点数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveNode = (nodeData) => {
  return request({
    url: '/core/node',
    method: 'post',
    data: nodeData
  }).then(response => {
    console.log('保存节点成功:', response);
    return response;
  }).catch(error => {
    console.error('保存节点失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 保存连线数据
 * @param {Object} lineData 连线数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveLine = (lineData) => {
  return request({
    url: '/core/line',
    method: 'post',
    data: lineData
  }).then(response => {
    console.log('保存连线成功:', response);
    return response;
  }).catch(error => {
    console.error('保存连线失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除连线
 * @param {string} lineId 连线ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteLine = (lineId) => {
  return request({
    url: `/core/line/${lineId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除连线 ${lineId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除连线 ${lineId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存节点样式
 * @param {Object} styleData 样式数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveNodeStyle = (styleData) => {
  return request({
    url: '/core/style',
    method: 'post',
    data: styleData
  }).then(response => {
    console.log('保存样式成功:', response);
    return response;
  }).catch(error => {
    console.error('保存样式失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 保存知识图谱数据
 * @param {string} courseId 知识图谱ID
 * @param {Object} data 知识图谱数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveCourseGraph = (courseId, data) => {
  // 将前端格式转换为后端所需的数据结构
  const requestData = {
    graph: {
      id: data.graphId || courseId,
      name: data.name || '未命名图谱',
      content: data.content || ''
    },
    nodes: data.nodes.map(node => ({
      id: node.id && !node.id.includes('node') ? Number(node.id) : null, // 如果是新节点(id格式如'node1')则不发送id
      graph_id: data.graphId || courseId,
      parent_id: node.parentId ? Number(node.parentId) : null,
      name: node.text || '',
      content: node.content || ''
    })),
    lines: data.links.map(link => ({
      id: link.id && !link.id.includes('line') ? Number(link.id) : null,
      node_id: Number(link.from),
      target_id: Number(link.to),
      content: link.text || ''
    })),
    nodeStyles: data.nodes.map(node => ({
      node_id: Number(node.id),
      type: node.type || '',
      color: node.color || '',
      fontColor: node.fontColor || '',
      node_shape: node.nodeShape || 0,
      width: node.width || null,
      height: node.height || null,
      border_width: node.borderWidth || null,
      border_height: node.borderHeight || null,
      fixed: node.fixed || '',
      x: node.x || null,
      y: node.y || null
    }))
  };

  return request({
    url: `/api/graph/${courseId}`,
    method: 'post',
    data: requestData
  }).catch(error => {
    console.error(`保存知识图谱 ${courseId} 数据失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存知识图谱大纲
 * @param {string} courseId 知识图谱ID
 * @param {Object} data 大纲数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveCourseOutline = (courseId, data) => {
  return request({
    url: `/api/courses/${courseId}/outline`,
    method: 'post',
    data
  }).catch(error => {
    console.error(`保存知识图谱 ${courseId} 大纲失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 从大纲创建知识图谱
 * @param {string} courseId 知识图谱ID
 * @returns {Promise<Object>} 知识图谱数据
 */
export const createGraphFromOutline = (courseId) => {
  console.log(`从大纲创建知识图谱: ${courseId}`);
  return request({
    url: `/api/outline/courses/${courseId}/outline-to-graph`,
    method: 'post'
  }).catch(error => {
    console.error(`从大纲创建知识图谱失败: ${courseId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 从知识图谱更新大纲
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 更新结果
 */
export const updateOutlineFromGraph = (graphId) => {
  console.log(`从知识图谱更新大纲: ${graphId}`);
  return request({
    url: `/api/graph/graphs/${graphId}/graph-to-outline`,
    method: 'post'
  }).catch(error => {
    console.error(`从知识图谱更新大纲失败: ${graphId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 同步节点映射关系
 * @param {string} courseId 知识图谱ID
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 同步结果
 */
export const syncNodeMappings = (courseId, graphId) => {
  console.log(`同步节点映射关系: 知识图谱${courseId}, 图谱${graphId}`);
  return request({
    url: `/api/graph/courses/${courseId}/graphs/${graphId}/sync-mappings`,
    method: 'post'
  }).catch(error => {
    console.error(`同步节点映射关系失败: 知识图谱${courseId}, 图谱${graphId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取大纲节点关联的知识点
 * @param {string} courseId 知识图谱ID
 * @param {string} nodeId 大纲节点ID
 * @returns {Promise<Array>} 关联的知识点列表
 */
export const getKnowledgeNodesForOutlineNode = (courseId, nodeId) => {
  console.log(`获取大纲节点关联的知识点: 知识图谱${courseId}, 节点${nodeId}`);
  return request({
    url: `/api/outline/courses/${courseId}/outline-nodes/${nodeId}/knowledge-nodes`,
    method: 'get'
  }).then(response => {
    if (response && response.success && Array.isArray(response.data)) {
      return response.data;
    }
    return response || [];
  }).catch(error => {
    console.error(`获取大纲节点关联的知识点失败: 知识图谱${courseId}, 节点${nodeId}`, error);
    return [];
  });
};

/**
 * 获取知识点关联的大纲节点
 * @param {string} nodeId 知识点ID
 * @returns {Promise<Array>} 关联的大纲节点列表
 */
export const getOutlineNodesForKnowledgeNode = (nodeId) => {
  console.log(`获取知识点关联的大纲节点: 知识点${nodeId}`);
  return request({
    url: `/api/graph/knowledge-nodes/${nodeId}/outline-nodes`,
    method: 'get'
  }).then(response => {
    if (response && response.success && Array.isArray(response.data)) {
      return response.data;
    }
    return response || [];
  }).catch(error => {
    console.error(`获取知识点关联的大纲节点失败: 知识点${nodeId}`, error);
    return [];
  });
};

// 知识图谱相关的函数别名 - 更明确的命名
export const getKnowledgeGraphList = getGraphList;
export const getKnowledgeGraphNodes = getNodeList;
export const createKnowledgeGraph = createGraph;
export const updateKnowledgeGraph = updateGraph;
export const deleteKnowledgeGraph = deleteGraph; 