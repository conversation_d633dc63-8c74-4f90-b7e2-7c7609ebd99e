import request from './axios';

/**
 * 获取作业管理列表
 * @param {Object} params 查询参数
 * @param {Object} params.clWork 作业对象
 * @param {string} params.clWork.createBy 创建者
 * @param {string} params.clWork.createTime 创建时间
 * @param {string} params.clWork.updateBy 更新者
 * @param {string} params.clWork.updateTime 更新时间
 * @param {string} params.clWork.remark 备注
 * @param {Object} params.clWork.params 其他参数
 * @param {number} params.clWork.id 作业ID
 * @param {string} params.clWork.name 作业名称
 * @param {string} params.clWork.gettFile 教师上传文件
 * @param {string} params.clWork.gettContent 教师上传内容
 * @param {string} params.clWork.getsFile 学生上传文件
 * @param {string} params.clWork.getsContent 学生上传内容
 * @param {number} params.clWork.courseId 所属课程ID
 * @param {string} params.clWork.overTime 作业截止时间
 * @returns {Promise<Object>} 作业列表响应数据，格式为TableDataInfo: { total, rows, code, msg }
 */
export const getHomeworkList = (params = {}) => {
  console.log('正在获取作业列表...', params);

  // 构建查询参数，过滤掉空值
  const cleanParams = {};

  // 如果传入了clWork对象，使用点号表示法构建查询参数
  if (params.clWork && typeof params.clWork === 'object') {
    Object.keys(params.clWork).forEach(key => {
      if (params.clWork[key] !== null && params.clWork[key] !== undefined && params.clWork[key] !== '') {
        cleanParams[`clWork.${key}`] = params.clWork[key];
      }
    });
  }

  // 处理其他直接传入的参数
  Object.keys(params).forEach(key => {
    if (key !== 'clWork' && params[key] !== null && params[key] !== undefined && params[key] !== '') {
      cleanParams[key] = params[key];
    }
  });

  console.log('构建的查询参数:', cleanParams);

  return request({
    url: '/core/work/list',
    method: 'get',
    params: cleanParams,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('获取作业列表成功:', response);
    
    // 根据接口文档，响应格式为TableDataInfo: { total, rows, code, msg }
    if (response && typeof response === 'object') {
      if (response.rows && Array.isArray(response.rows)) {
        console.log('返回标准TableDataInfo格式');
        return {
          total: response.total || response.rows.length,
          rows: response.rows,
          code: response.code || 0,
          msg: response.msg || '成功'
        };
      } else if (Array.isArray(response)) {
        console.log('返回数组格式，转换为TableDataInfo');
        return {
          total: response.length,
          rows: response,
          code: 0,
          msg: '成功'
        };
      } else if (response.data && Array.isArray(response.data)) {
        console.log('从嵌套响应中提取作业列表数据');
        return {
          total: response.total || response.data.length,
          rows: response.data,
          code: response.code || 0,
          msg: response.msg || '成功'
        };
      }
    }
    
    console.warn('响应格式不符合预期，返回空结果');
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: '响应格式不符合预期'
    };
  }).catch(error => {
    console.error('获取作业列表失败:', error);
    // 返回错误格式，保持与TableDataInfo一致
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: error.message || '获取作业列表失败'
    };
  });
};

/**
 * 根据课程ID获取作业列表
 * @param {number} courseId 课程ID
 * @param {Object} additionalParams 额外的查询参数
 * @returns {Promise<Object>} 作业列表响应数据
 */
export const getHomeworkListByCourse = (courseId, additionalParams = {}) => {
  console.log(`正在获取课程${courseId}的作业列表...`, additionalParams);
  
  if (!courseId) {
    console.error('获取作业列表失败: 课程ID不能为空');
    return Promise.resolve({
      total: 0,
      rows: [],
      code: -1,
      msg: '课程ID不能为空'
    });
  }

  const params = {
    clWork: {
      courseId: courseId,
      ...additionalParams
    }
  };

  return getHomeworkList(params);
};

/**
 * 创建新作业
 * @param {Object} homeworkData 作业数据
 * @param {string} homeworkData.name 作业名称
 * @param {string} homeworkData.gettFile 教师上传文件
 * @param {string} homeworkData.gettContent 教师上传内容
 * @param {string} homeworkData.getsFile 学生上传文件
 * @param {string} homeworkData.getsContent 学生上传内容
 * @param {number} homeworkData.courseId 所属课程ID
 * @param {string} homeworkData.overTime 作业截止时间
 * @param {string} homeworkData.remark 备注
 * @returns {Promise<Object>} 创建结果
 */
export const createHomework = (homeworkData) => {
  console.log('正在创建新作业...', homeworkData);
  
  // 验证必填字段
  if (!homeworkData.name || !homeworkData.courseId) {
    console.error('创建作业失败: 作业名称和课程ID不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      msg: '作业名称和课程ID不能为空',
      data: null
    });
  }

  // 构建请求数据
  const cleanData = {};

  // 不手动添加创建时间，让后端自动处理
  // const now = new Date();
  // cleanData.createTime = formatDateForBackend(now);

  Object.keys(homeworkData).forEach(key => {
    // 特殊处理时间字段
    if (key === 'overTime' || key === 'createTime' || key === 'updateTime') {
      let value = homeworkData[key];

      if (value && value !== '') {
        try {
          let dateValue = value;

          // 如果是datetime-local格式（YYYY-MM-DDTHH:mm），添加秒数
          if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)) {
            dateValue = value + ':00';
          }

          const date = new Date(dateValue);
          if (!isNaN(date.getTime())) {
            // 使用简化ISO格式: yyyy-MM-ddTHH:mm:ss
            value = date.toISOString().split('.')[0];
          }
        } catch (error) {
          console.warn(`时间格式转换失败:`, value, error);
          value = null;
        }
      } else {
        value = null;
      }

      cleanData[key] = value;
    } else if (homeworkData[key] !== null && homeworkData[key] !== undefined && homeworkData[key] !== '') {
      cleanData[key] = homeworkData[key];
    }
  });

  return request({
    url: '/core/work',
    method: 'post',
    data: cleanData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('创建作业成功:', response);
    return response;
  }).catch(error => {
    console.error('创建作业失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 更新作业
 * @param {Object} homeworkData 作业数据（必须包含id字段）
 * @param {number} homeworkData.id 作业ID
 * @param {string} homeworkData.name 作业名称
 * @param {string} homeworkData.gettFile 教师上传文件
 * @param {string} homeworkData.gettContent 教师上传内容
 * @param {string} homeworkData.getsFile 学生上传文件
 * @param {string} homeworkData.getsContent 学生上传内容
 * @param {number} homeworkData.courseId 所属课程ID
 * @param {string} homeworkData.overTime 作业截止时间
 * @param {string} homeworkData.remark 备注
 * @returns {Promise<Object>} 更新结果
 */
export const updateHomework = (homeworkData) => {
  console.log('正在更新作业...', homeworkData);
  
  // 验证必填字段
  if (!homeworkData.id) {
    console.error('更新作业失败: 作业ID不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      msg: '作业ID不能为空',
      data: null
    });
  }

  // 构建请求数据，过滤掉空值
  const cleanData = {};
  Object.keys(homeworkData).forEach(key => {
    // 特殊处理时间字段
    if (key === 'overTime' || key === 'createTime' || key === 'updateTime') {
      let value = homeworkData[key];

      if (value && value !== '') {
        try {
          let dateValue = value;

          // 如果是datetime-local格式（YYYY-MM-DDTHH:mm），添加秒数
          if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/)) {
            dateValue = value + ':00';
          }

          const date = new Date(dateValue);
          if (!isNaN(date.getTime())) {
            // 使用简化ISO格式: yyyy-MM-ddTHH:mm:ss
            value = date.toISOString().split('.')[0];
          }
        } catch (error) {
          console.warn(`时间格式转换失败:`, value, error);
          value = null;
        }
      } else {
        value = null;
      }

      cleanData[key] = value;
    } else if (homeworkData[key] !== null && homeworkData[key] !== undefined && homeworkData[key] !== '') {
      cleanData[key] = homeworkData[key];
    }
  });



  return request({
    url: '/core/work',
    method: 'put',
    data: cleanData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('更新作业成功:', response);
    return response;
  }).catch(error => {
    console.error('更新作业失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除作业
 * @param {number|string} homeworkId 作业ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteHomework = (homeworkId) => {
  console.log(`正在删除作业: ${homeworkId}`);
  
  if (!homeworkId) {
    console.error('删除作业失败: 作业ID不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      msg: '作业ID不能为空',
      data: null
    });
  }

  return request({
    url: `/core/work/${homeworkId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除作业${homeworkId}成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除作业${homeworkId}失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 根据ID获取作业详情
 * @param {number|string} homeworkId 作业ID
 * @returns {Promise<Object>} 作业详情数据，响应格式为AjaxResult: { error, success, warn, empty, data }
 */
export const getHomeworkById = (homeworkId) => {
  console.log(`正在获取作业详情: ${homeworkId}`);

  if (!homeworkId) {
    console.error('获取作业详情失败: 作业ID不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: '作业ID不能为空',
      data: null
    });
  }

  return request({
    url: `/core/work/${homeworkId}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log(`获取作业${homeworkId}详情成功:`, response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准AjaxResult格式：{ error, success, warn, empty, data }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式');
        return {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '获取成功' : '获取失败'),
          data: response.data || null
        };
      }
    }

    console.warn('响应格式不符合预期，返回默认成功结果');
    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '获取成功',
      data: response
    };
  }).catch(error => {
    console.error(`获取作业${homeworkId}详情失败:`, error);
    // 返回错误格式，保持与AjaxResult一致
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '获取作业详情失败',
      data: null
    };
  });
};
